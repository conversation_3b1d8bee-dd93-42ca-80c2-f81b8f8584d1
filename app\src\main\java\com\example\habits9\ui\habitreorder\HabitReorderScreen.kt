package com.example.habits9.ui.habitreorder

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.animateOffsetAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectDragGesturesAfterLongPress
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.zIndex
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.DragHandle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.compositeOver
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel

// Colors from style guide
private val BackgroundDark = Color(0xFF121826)
private val TextPrimary = Color(0xFFE2E8F0)
private val TextSecondary = Color(0xFFA0AEC0)
private val AccentPrimary = Color(0xFF81E6D9)
private val SurfaceVariantDark = Color(0xFF1A202C)
private val DividerColor = Color(0xFF2D3748)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HabitReorderScreen(
    onBackClick: () -> Unit,
    viewModel: HabitReorderViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val hapticFeedback = LocalHapticFeedback.current

    // Save and clear state when navigating back to ensure persistence
    val handleBackClick = {
        viewModel.saveOrder()
        viewModel.clearReorderedState()
        onBackClick()
    }

    // Save and clear state when screen is disposed (e.g., system back button)
    DisposableEffect(Unit) {
        onDispose {
            viewModel.saveOrder()
            viewModel.clearReorderedState()
        }
    }

    // Enhanced drag state for smoother experience
    var draggedItemIndex by remember { mutableStateOf(-1) }
    var dragOffset by remember { mutableStateOf(Offset.Zero) }
    var targetDropIndex by remember { mutableStateOf(-1) }
    var isDragging by remember { mutableStateOf(false) }
    val listState = rememberLazyListState()

    // Smooth animated drag offset for better finger tracking
    val animatedDragOffset by animateOffsetAsState(
        targetValue = dragOffset,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioNoBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "dragOffset"
    )

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Reorder Habits",
                        color = TextPrimary,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = handleBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = TextPrimary
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = SurfaceVariantDark
                )
            )
        },
        containerColor = BackgroundDark
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (uiState.habits.isEmpty()) {
                // Empty state
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(32.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "You have no habits to organize.",
                        color = TextPrimary,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Go back and add a new habit to get started!",
                        color = TextSecondary,
                        fontSize = 14.sp
                    )
                }
            } else {
                // Habit list
                LazyColumn(
                    state = listState,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    itemsIndexed(
                        items = uiState.habits,
                        key = { index, habit -> "${habit.uuid}_reorder_$index" }
                    ) { index, habit ->
                        val isDraggingThisItem = draggedItemIndex == index

                        // Calculate displacement for smooth item movement
                        val displacement = when {
                            isDraggingThisItem -> 0f
                            isDragging && draggedItemIndex != -1 -> {
                                // Items between original and current position get displaced
                                val originalIndex = draggedItemIndex
                                val currentIndex = index
                                when {
                                    originalIndex < currentIndex && targetDropIndex >= currentIndex -> -1f
                                    originalIndex > currentIndex && targetDropIndex <= currentIndex -> 1f
                                    else -> 0f
                                }
                            }
                            else -> 0f
                        }

                        val animatedDisplacement by animateFloatAsState(
                            targetValue = displacement,
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioLowBouncy,
                                stiffness = Spring.StiffnessMediumLow
                            ),
                            label = "displacement_$index"
                        )

                        HabitReorderItem(
                            habitName = habit.name,
                            isDragging = isDraggingThisItem,
                            dragOffset = if (isDraggingThisItem) animatedDragOffset else Offset.Zero,
                            isDropTarget = targetDropIndex == index && !isDraggingThisItem,
                            displacement = animatedDisplacement,
                            onDragStart = {
                                draggedItemIndex = index
                                dragOffset = Offset.Zero
                                targetDropIndex = -1
                                isDragging = true
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                            },
                            onDrag = { dragAmount ->
                                dragOffset += dragAmount

                                // Enhanced drop target calculation with smoother feedback
                                val visibleItems = listState.layoutInfo.visibleItemsInfo
                                val currentItemInfo = visibleItems.find { it.index == draggedItemIndex }

                                if (currentItemInfo != null) {
                                    val itemCenterY = currentItemInfo.offset + currentItemInfo.size / 2 + dragOffset.y

                                    // Find the best drop target with distance-based calculation
                                    var bestTarget: Int? = null
                                    var minDistance = Float.MAX_VALUE

                                    for (item in visibleItems) {
                                        if (item.index == draggedItemIndex) continue

                                        val itemCenter = item.offset + item.size / 2
                                        val distance = kotlin.math.abs(itemCenterY - itemCenter)

                                        if (distance < minDistance && distance < item.size * 0.5f) {
                                            minDistance = distance
                                            bestTarget = item.index
                                        }
                                    }

                                    // Update target drop index for visual feedback
                                    if (bestTarget != null && bestTarget != targetDropIndex) {
                                        targetDropIndex = bestTarget
                                        hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                    } else if (bestTarget == null) {
                                        targetDropIndex = -1
                                    }

                                    // Only perform actual reorder when crossing significant threshold
                                    if (bestTarget != null && bestTarget != draggedItemIndex && minDistance < currentItemInfo.size * 0.3f) {
                                        viewModel.moveHabit(draggedItemIndex, bestTarget)
                                        draggedItemIndex = bestTarget
                                        // Don't reset dragOffset to maintain smooth tracking
                                    }
                                }
                            },
                            onDragEnd = {
                                draggedItemIndex = -1
                                dragOffset = Offset.Zero
                                targetDropIndex = -1
                                isDragging = false
                                viewModel.saveOrder()
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun HabitReorderItem(
    habitName: String,
    isDragging: Boolean,
    dragOffset: Offset,
    isDropTarget: Boolean = false,
    displacement: Float = 0f,
    onDragStart: () -> Unit,
    onDrag: (Offset) -> Unit,
    onDragEnd: () -> Unit,
    modifier: Modifier = Modifier
) {
    // Enhanced visual feedback with smooth spring animations
    val animatedElevation by animateDpAsState(
        targetValue = when {
            isDragging -> 16.dp
            isDropTarget -> 8.dp
            else -> 2.dp
        },
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioLowBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "elevation"
    )

    val animatedScale by animateFloatAsState(
        targetValue = when {
            isDragging -> 1.05f
            isDropTarget -> 1.02f
            else -> 1f
        },
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioLowBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "scale"
    )

    val animatedAlpha by animateFloatAsState(
        targetValue = when {
            isDragging -> 0.95f
            isDropTarget -> 0.85f
            else -> 1f
        },
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "alpha"
    )

    Card(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = animatedElevation,
                shape = RoundedCornerShape(12.dp)
            )
            .graphicsLayer {
                // Hardware-accelerated smooth finger tracking and displacement
                translationX = dragOffset.x
                translationY = dragOffset.y + (displacement * 80.dp.toPx()) // 80dp is approximate item height
                scaleX = animatedScale
                scaleY = animatedScale
                alpha = animatedAlpha
            }
            .zIndex(if (isDragging) 10f else if (isDropTarget) 5f else 1f)
            .pointerInput(Unit) {
                detectDragGesturesAfterLongPress(
                    onDragStart = { offset ->
                        onDragStart()
                    },
                    onDrag = { change, dragAmount ->
                        change.consume()
                        onDrag(dragAmount)
                    },
                    onDragEnd = {
                        onDragEnd()
                    },
                    onDragCancel = {
                        onDragEnd()
                    }
                )
            },
        colors = CardDefaults.cardColors(
            containerColor = when {
                isDragging -> AccentPrimary.copy(alpha = 0.15f).compositeOver(SurfaceVariantDark)
                isDropTarget -> AccentPrimary.copy(alpha = 0.08f).compositeOver(SurfaceVariantDark)
                else -> SurfaceVariantDark
            }
        ),
        shape = RoundedCornerShape(12.dp),
        border = when {
            isDragging -> BorderStroke(2.dp, AccentPrimary.copy(alpha = 0.4f))
            isDropTarget -> BorderStroke(1.dp, AccentPrimary.copy(alpha = 0.2f))
            else -> null
        }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Enhanced drag handle with smooth visual feedback
            Icon(
                imageVector = Icons.Default.DragHandle,
                contentDescription = "Drag to reorder",
                tint = when {
                    isDragging -> AccentPrimary
                    isDropTarget -> AccentPrimary.copy(alpha = 0.7f)
                    else -> TextSecondary
                },
                modifier = Modifier
                    .size(24.dp)
                    .padding(2.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Text(
                text = habitName,
                color = when {
                    isDragging -> TextPrimary.copy(alpha = 0.95f)
                    isDropTarget -> TextPrimary.copy(alpha = 0.8f)
                    else -> TextPrimary
                },
                fontSize = 16.sp,
                fontWeight = when {
                    isDragging -> FontWeight.SemiBold
                    isDropTarget -> FontWeight.Medium
                    else -> FontWeight.Medium
                },
                modifier = Modifier.weight(1f)
            )
        }
    }
}
