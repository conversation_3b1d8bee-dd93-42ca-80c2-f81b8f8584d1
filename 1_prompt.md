# Prompt: Implement High-Fidelity Drag-and-Drop Animations

## 1. Objective & Context

The objective is to elevate the user experience of the "Reorder Habits" screen by replacing the current drag-and-drop implementation with a high-fidelity, smooth animation system. The goal is to create an interaction that feels fluid, intuitive, and professional, based on the detailed specifications below.

The current implementation is functional but lacks the visual polish that gives users confidence and clarity during the reordering action. We will now implement a best-in-class animation.

## 2. Detailed Implementation Plan

The new animation can be broken down into three distinct phases: the **Lift**, the **Drag**, and the **Drop**.

### Part 1: The "Lift" Effect (On Drag Start)

This phase provides immediate feedback that an item has been selected and is ready to be moved.

**Task 2.1: Implement Elevation and Scaling**

- When a user long-presses the drag handle of a habit item, that item must visually "lift" off the surface of the list.
- Achieve this by:
  1.  Applying a noticeable elevation shadow to the item view.
  2.  Slightly increasing the scale of the item (e.g., to 1.05x) to make it feel prominent.
  - Both changes should be animated smoothly over approximately 150ms.

**Task 2.2: Change Background Color**

- Simultaneously, the background color of the selected item should transition to the `surface-variant` color token defined in our project's style guide. This visually separates the active item from the static ones. This color change should also be animated.

### Part 2: The "Drag" Effect (During Movement)

This phase provides clear guidance on where the item can be placed.

**Task 2.3: Implement Smooth Item Displacement**

- This is the most critical part of the animation. As the user drags the selected item up or down the list, the other items must not snap into new positions.
- Instead, they should **animate smoothly** out of the way to create a visible gap for the dragged item.
- The animation of the other items sliding up or down should be fluid and track the position of the user's finger.

### Part 3: The "Drop" Effect (On Drag End)

This phase concludes the interaction and settles the item into its new home.

**Task 2.4: Implement Settle Animation**

- When the user releases the dragged item, it should not just appear in the new position. It must animate smoothly into the gap created by the displaced items.
- As it settles, the "Lift" effect must be reversed:
  1.  The elevation shadow should animate away.
  2.  The item should animate back to its normal scale (1.0x).
  3.  The background color should transition back to its default state.
- This entire "settle" animation should be cohesive and fluid.

## 3. Verification Plan

Please follow these steps meticulously to confirm the new animations are implemented correctly.

1.  Navigate to the screen for reordering habits.
2.  Long-press the drag handle of any habit.
3.  **Verify:** The item smoothly lifts, a shadow appears, its scale increases slightly, and its background color changes.
4.  Drag the item slowly up and down the list without releasing it.
5.  **Verify (CRITICAL):** The other items in the list slide smoothly out of the way, creating a clear, animated gap that follows your finger's position. There should be no stuttering or snapping.
6.  Release the item in a new position.
7.  **Verify:** The item animates smoothly into the gap, and its appearance (shadow, scale, color) animates back to its normal state.
8.  Test the interaction with a long list of habits (if possible) to ensure performance remains smooth.

## 4. Mandatory Development Guidelines

- **Refer to the Style Guide:** Before starting any feature, always consult the project's style guide for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns. It is the single source of truth for styling decisions.
- **Study the Reference Project:** Prior to implementation, review the reference project to understand how similar features have been approached to maintain consistency and avoid duplications or contradictions. The reference project serves as a blueprint for implementation. This step is mandatory. Do not proceed to implementation without this step.the location of the reference project is `./uhabits-dev`
- **Understand the Existing Project Structure:** Before writing any code, spend time exploring and understanding how the current system is structured. Even for new features, existing components or utility functions may be reusable. Integrate changes cleanly into the existing architecture instead of creating disconnected code.
- **Maintain a Clean Codebase:** After implementing features, remove any temporary, test, or duplicate files, folders, or unused components that were created during development. Keep the codebase organized and clutter-free.
- **Pause If There Is Any Confusion:** If at any point the requirements are unclear, do not proceed based on assumptions. Immediately pause and seek clarification. It is better to get clarity than to redo or fix avoidable mistakes later.
- **Remove Unused Old Implementations:** As part of final review, identify and delete any old, unused code that was implemented earlier but is no longer in use. This includes obsolete modules, features, or legacy logic.
