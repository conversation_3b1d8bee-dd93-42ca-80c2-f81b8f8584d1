
> Task :app:compileDebugKotlin FAILED
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/habitreorder/HabitReorderScreen.kt:6:40 Unresolved reference 'animateColorAsState'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/habitreorder/HabitReorderScreen.kt:353:36 Unresolved reference 'animateColorAsState'.

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugKotlin'.
> A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   > Compilation error. See log for more details

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 2s
30 actionable tasks: 2 executed, 4 from cache, 24 up-to-date
