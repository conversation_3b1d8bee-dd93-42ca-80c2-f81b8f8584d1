# Error Resolution: Duplicate Habits After Reordering

## Issue

After reordering habits using the custom sort feature and returning to the home page, duplicate habits are created. If there were originally 2 habits, there are now 4 habits total (each habit is duplicated).

## Root Cause Analysis

### Problem Flow

1. User reorders habits in HabitReorderScreen
2. HabitReorderViewModel.saveOrder() is called multiple times:
   - When user navigates back (handleBackClick)
   - When screen is disposed (DisposableEffect onDispose)
   - When drag ends (onDragEnd)
3. saveOrder() attempts batch update via updateCustomOrderIndices()
4. If batch update fails, it falls back to individual updateHabit() calls
5. **ROOT CAUSE**: updateHabit() was using habit.id (hash-based Long) as Firestore document ID instead of the actual Firestore document ID
6. This caused new documents to be created instead of updating existing ones

### Technical Details

- `habit.id` is a hash-based Long value generated from the Firestore document ID
- Firestore document IDs are auto-generated strings (e.g., "abc123def456")
- Using `habit.id.toString()` as document ID creates new documents with different IDs
- Each updateHabit() call created a duplicate habit instead of updating the existing one

## Solution

### Fixed updateHabit() Method

Modified the updateHabit() method in HabitRepository to:

1. **Find Document by UUID**: Query all habits to find the actual Firestore document ID by matching the habit's UUID
2. **Use Correct Document ID**: Use the actual Firestore document ID for the update operation
3. **Prevent Duplicates**: Ensure updates modify existing documents instead of creating new ones

### Key Changes

- Added document lookup by UUID before update
- Use actual Firestore document ID instead of hash-based habit.id
- Enhanced error handling and logging
- Proper validation to ensure habit exists before update

## Files Modified

- `app/src/main/java/com/example/habits9/data/HabitRepository.kt` (updateHabit method, lines 221-295)

## Status

✅ RESOLVED - Fixed updateHabit() method to use correct Firestore document IDs, preventing duplicate habit creation during reordering operations.

## Prevention

This fix ensures that:

1. Habit updates modify existing documents instead of creating duplicates
2. The batch update (updateCustomOrderIndices) remains the preferred method
3. Individual updates (updateHabit) now work correctly as a fallback
4. Proper error handling prevents silent failures
