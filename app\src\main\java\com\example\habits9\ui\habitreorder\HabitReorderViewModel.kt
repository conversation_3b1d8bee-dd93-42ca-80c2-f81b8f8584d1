package com.example.habits9.ui.habitreorder

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.habits9.data.Habit
import com.example.habits9.data.HabitRepository
import com.example.habits9.data.HabitSortType
import com.example.habits9.data.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

data class HabitReorderUiState(
    val habits: List<Habit> = emptyList(),
    val isLoading: Boolean = false
)

@HiltViewModel
class HabitReorderViewModel @Inject constructor(
    private val habitRepository: HabitRepository,
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {

    private val _reorderedHabits = MutableStateFlow<List<Habit>>(emptyList())

    val uiState: StateFlow<HabitReorderUiState> = combine(
        habitRepository.getAllHabits(),
        _reorderedHabits
    ) { allHabits, reorderedHabits ->
        val habitsToShow = if (reorderedHabits.isNotEmpty()) {
            reorderedHabits
        } else {
            // Prefer customOrderIndex if present, fall back to position
            allHabits.sortedWith(
                compareBy<Habit> { if (it.customOrderIndex >= 0) it.customOrderIndex else Int.MAX_VALUE }
                    .thenBy { it.position }
            )
        }
        
        HabitReorderUiState(
            habits = habitsToShow,
            isLoading = false
        )
    }.stateIn(
        scope = viewModelScope,
        started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
        initialValue = HabitReorderUiState(isLoading = true)
    )

    init {
        // Set sort type to custom order when entering this screen
        viewModelScope.launch {
            userPreferencesRepository.updateHabitSortType(HabitSortType.CUSTOM_ORDER)
        }
    }

    /**
     * Move a habit from one position to another
     */
    fun moveHabit(fromIndex: Int, toIndex: Int) {
        val currentHabits = _reorderedHabits.value.ifEmpty { 
            uiState.value.habits 
        }
        
        if (fromIndex < 0 || fromIndex >= currentHabits.size || 
            toIndex < 0 || toIndex >= currentHabits.size || 
            fromIndex == toIndex) {
            return
        }

        val mutableList = currentHabits.toMutableList()
        val item = mutableList.removeAt(fromIndex)
        mutableList.add(toIndex, item)
        
        _reorderedHabits.value = mutableList
    }

    /**
     * Save the current order using customOrderIndex for persistent ordering
     */
    fun saveOrder() {
        val listToSave = _reorderedHabits.value.ifEmpty { uiState.value.habits }
        android.util.Log.d("HabitReorderViewModel", "saveOrder() called with ${listToSave.size} habits")
        saveOrder(listToSave)
    }

    /**
     * Overload that accepts an explicit reordered list as per spec.
     */
    fun saveOrder(finalHabits: List<Habit>) {
        viewModelScope.launch {
            val currentHabits = if (finalHabits.isNotEmpty()) finalHabits else _reorderedHabits.value.ifEmpty { uiState.value.habits }

            if (currentHabits.isNotEmpty()) {
                // Create a map of habit UUID to custom order index
                val habitOrderMap = currentHabits.mapIndexed { index, habit ->
                    habit.uuid to index
                }.toMap()

                // Update custom order indices in batch for better performance and atomicity
                val success = habitRepository.updateCustomOrderIndices(habitOrderMap)

                if (success) {
                    // Also save custom order to preferences as backup
                    val habitUuids = currentHabits.map { it.uuid }
                    userPreferencesRepository.updateCustomHabitOrder(habitUuids)
                    android.util.Log.d("HabitReorderViewModel", "Batch update successful for ${currentHabits.size} habits")
                } else {
                    // If batch update fails, fall back to individual updates
                    android.util.Log.w("HabitReorderViewModel", "Batch update failed, falling back to individual updates")
                    val updatedHabits = currentHabits.mapIndexed { index, habit ->
                        habit.copy(customOrderIndex = index)
                    }

                    updatedHabits.forEach { habit ->
                        try {
                            android.util.Log.d("HabitReorderViewModel", "Updating individual habit: ${habit.uuid}")
                            habitRepository.updateHabit(habit)
                        } catch (e: Exception) {
                            // Log error but continue with other habits
                            android.util.Log.e("HabitReorderViewModel", "Failed to update habit ${habit.uuid}", e)
                        }
                    }
                }
            }
        }
    }

    /**
     * Clear the reordered habits state to prevent stale state issues
     */
    fun clearReorderedState() {
        _reorderedHabits.value = emptyList()
    }
}
